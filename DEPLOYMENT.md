# ESL Learn - Deployment Guide

Eto yung guide para ma-deploy nimo ang ESL Learn static website sa different hosting platforms.

## Build Process

Naka-optimize na ni siya for static deployment with these features:

- **Hash-based routing** - para sa client-side navigation
- **Optimized bundle splitting** - mas better ang caching ani
- **Source maps** - para debugging kung naa problema
- **Modern ES2015+ target** - mas gamay ang bundles
- **CSS code splitting** - mas paspas ang loading

## Building for Production

```bash
# Standard production build lang
npm run build

# Build with production optimizations - mas optimized pa
npm run build:production

# Build with bundle analysis - makita nimo kung unsa ka dako ang files
npm run build:analyze
```

Ang build output naa sa `dist/` directory.

## Deployment Options

### 1. Netlify (Recommended pa ni)

1. Connect lang ang repo nimo sa Netlify
2. Set build command: `npm run build`
3. Set publish directory: `dist`
4. Auto deploy na siya every push - convenient kaayo

**Netlify Configuration (_netlify.toml):**
```toml
[build]
  command = "npm run build"
  publish = "dist"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
```

### 2. Vercel

1. Connect lang ang repo nimo sa Vercel
2. Framework preset: Vite
3. Build command: `npm run build`
4. Output directory: `dist`

### 3. GitHub Pages

1. Build ang project: `npm run build`
2. Push ang `dist` folder sa `gh-pages` branch
3. Enable GitHub Pages sa repository settings

**GitHub Actions Workflow (.github/workflows/deploy.yml):**
```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist
```

### 4. Firebase Hosting

1. Install Firebase CLI: `npm install -g firebase-tools`
2. Initialize: `firebase init hosting`
3. Set public directory: `dist`
4. Configure as SPA: Yes
5. Deploy: `firebase deploy`

### 5. AWS S3 + CloudFront

1. Build ang project: `npm run build`
2. Upload ang `dist` contents sa S3 bucket
3. Configure S3 for static website hosting
4. Set up CloudFront distribution
5. Configure error pages para redirect sa `index.html`

## Environment Variables

Para sa different environments, pwede ka mag-create og:

- `.env.development` - Development environment
- `.env.production` - Production environment
- `.env.local` - Local overrides (dili ni ma-commit)

Example:
```env
VITE_API_URL=https://api.esllearn.com
VITE_APP_NAME=ESL Learn
```

## Performance Optimizations

Naa nay mga optimizations sa build:

- **Code splitting**: Vendor libraries gi-separate para better caching
- **Tree shaking**: Unused code gi-eliminate na
- **Asset optimization**: Images ug fonts gi-optimize na
- **CSS purging**: Unused Tailwind classes gi-remove na
- **Compression**: Gzip compression enabled na

## Testing the Build

```bash
# Preview ang production build locally
npm run preview

# Run type checking - check kung naa errors
npm run type-check

# Run linting - check coding standards
npm run lint
```

## Troubleshooting

### Hash Routing Issues

Kung dili mo-work ang navigation sa hosting platform nimo:

1. Make sure ang server nimo nag-redirect sa tanan routes to `index.html`
2. Check kung tama ang base path configuration sa `vite.config.ts`

### Build Errors

Common issues ug solutions:

- **TypeScript errors**: Run `npm run type-check` para makita ang issues
- **Import errors**: Check ang file paths ug exports
- **Memory issues**: Increase Node.js memory: `NODE_OPTIONS="--max-old-space-size=4096" npm run build`

### Performance Issues

- Use `npm run build:analyze` para makita ang large bundles
- Consider lazy loading para sa large components
- Optimize images ug assets

## Browser Support

Ang build nag-target sa modern browsers (ES2015+):
- Chrome 61+
- Firefox 60+
- Safari 11+
- Edge 16+

Para sa older browser support, update ang `target` sa `vite.config.ts`.

## Security Considerations

- Tanan builds kay static files ra (walay server-side code)
- Ayaw ibutang sensitive data sa build
- Gamiton environment variables para sa configuration
- Enable HTTPS sa hosting platform nimo

## Monitoring and Analytics

Consider adding:
- Google Analytics or similar
- Error tracking (Sentry, LogRocket)
- Performance monitoring
- User feedback tools

## Next Steps

After deployment:
1. Set up custom domain
2. Configure SSL certificate
3. Set up monitoring
4. Add analytics
5. Test sa different devices ug browsers
