import React from 'react';
import { cn } from '../../utils';

interface CoursesSectionProps {
  className?: string;
}

interface CourseCategory {
  id: string;
  title: string;
  courseCount: number;
  color: string;
  size: 'small' | 'medium' | 'large';
  image: string;
  description: string;
}

const courseCategories: CourseCategory[] = [
  {
    id: '1',
    title: 'PROGRAMMING',
    courseCount: 22,
    color: 'bg-gradient-to-br from-blue-500 to-purple-600',
    size: 'large',
    image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=400&h=300&fit=crop&crop=center',
    description: 'Learn coding fundamentals and advanced programming concepts',
  },
  {
    id: '2',
    title: 'DEVELOPMENT',
    courseCount: 15,
    color: 'bg-gradient-to-br from-green-400 to-green-600',
    size: 'small',
    image: 'https://images.unsplash.com/photo-**********-4365d14bab8c?w=400&h=300&fit=crop&crop=center',
    description: 'Web and mobile app development skills',
  },
  {
    id: '3',
    title: 'HEALTH',
    courseCount: 78,
    color: 'bg-gradient-to-br from-purple-500 to-purple-700',
    size: 'large',
    image: 'https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=300&fit=crop&crop=center',
    description: 'Wellness, fitness, and healthcare education',
  },
  {
    id: '4',
    title: 'INTERIOR',
    courseCount: 31,
    color: 'bg-gradient-to-br from-blue-400 to-blue-600',
    size: 'small',
    image: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=center',
    description: 'Interior design and home decoration',
  },
  {
    id: '5',
    title: 'MUSIC',
    courseCount: 12,
    color: 'bg-gradient-to-br from-orange-400 to-orange-600',
    size: 'small',
    image: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=300&fit=crop&crop=center',
    description: 'Music theory, instruments, and production',
  },
  {
    id: '6',
    title: 'BUSINESS',
    courseCount: 34,
    color: 'bg-gradient-to-br from-blue-500 to-blue-700',
    size: 'large',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop&crop=center',
    description: 'Entrepreneurship and business management',
  },
  {
    id: '7',
    title: 'DESIGN',
    courseCount: 84,
    color: 'bg-gradient-to-br from-red-400 to-red-600',
    size: 'small',
    image: 'https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=300&fit=crop&crop=center',
    description: 'Graphic design and creative arts',
  },
  {
    id: '8',
    title: 'PHOTOGRAPHY',
    courseCount: 71,
    color: 'bg-gradient-to-br from-pink-400 to-pink-600',
    size: 'medium',
    image: 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=300&fit=crop&crop=center',
    description: 'Photography techniques and visual storytelling',
  },
];

const getSizeClasses = (size: string) => {
  switch (size) {
    case 'small':
      return 'h-32 w-full';
    case 'medium':
      return 'h-40 w-full';
    case 'large':
      return 'h-48 w-full';
    default:
      return 'h-32 w-full';
  }
};

const CourseCard: React.FC<{ category: CourseCategory }> = ({ category }) => (
  <div className={cn(
    'rounded-lg text-white overflow-hidden cursor-pointer transform hover:scale-[1.02] transition-all duration-300 shadow-md hover:shadow-lg relative group',
    getSizeClasses(category.size)
  )}>
    <div className="absolute inset-0">
      <img
        src={category.image}
        alt={category.title}
        className="w-full h-full object-cover"
      />
      <div className={cn('absolute inset-0 opacity-80 group-hover:opacity-70 transition-opacity', category.color)}></div>
    </div>
    <div className="relative z-10 p-3 h-full flex flex-col justify-end">
      <h3 className="text-sm font-bold mb-1 leading-tight">{category.title}</h3>
      <p className="text-xs opacity-90">{category.courseCount} Courses</p>
    </div>
  </div>
);

export const CoursesSection: React.FC<CoursesSectionProps> = ({ className }) => {
  return (
    <section className={cn('py-20 bg-gray-50', className)}>
      <div className="container-custom">
        {/* Section Header */}
        <div className="text-center mb-16">
          <p className="text-sm font-semibold text-primary-600 uppercase tracking-wider mb-4">
            EXPAND YOUR HORIZONS
          </p>
          <h2 className="text-3xl md:text-4xl font-bold text-secondary-900 font-heading mb-4">
            Explore Learning Categories
          </h2>
          <p className="text-xl text-secondary-600 max-w-3xl mx-auto">
            Discover diverse learning opportunities beyond English. From programming to photography,
            expand your skillset with expert-led courses designed for global learners.
          </p>
        </div>

        {/* Courses Grid */}
        <div className="max-w-5xl mx-auto">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
            {/* Column 1 */}
            <div className="space-y-3 md:space-y-4">
              <CourseCard category={courseCategories[0]} />
              <CourseCard category={courseCategories[4]} />
            </div>

            {/* Column 2 */}
            <div className="space-y-3 md:space-y-4">
              <CourseCard category={courseCategories[1]} />
              <CourseCard category={courseCategories[5]} />
            </div>

            {/* Column 3 */}
            <div className="space-y-3 md:space-y-4">
              <CourseCard category={courseCategories[2]} />
              <CourseCard category={courseCategories[6]} />
            </div>

            {/* Column 4 */}
            <div className="space-y-3 md:space-y-4">
              <CourseCard category={courseCategories[3]} />
              <CourseCard category={courseCategories[7]} />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
