import React, { useEffect, useRef } from 'react';
import { cn } from '../../utils';

interface CallToActionSectionProps {
  className?: string;
}

export const CallToActionSection: React.FC<CallToActionSectionProps> = ({ className }) => {
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate');
          }
        });
      },
      { threshold: 0.1 }
    );

    const animatedElements = sectionRef.current?.querySelectorAll('.animate-on-scroll, .animate-on-scroll-scale');
    animatedElements?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  return (
    <section
      ref={sectionRef}
      className={cn('section-padding relative overflow-hidden bg-gradient-to-b from-gray-50 to-white', className)}
    >
      {/* Clean Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Subtle Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full blur-2xl floating-element opacity-30" />
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-green-100 to-blue-100 rounded-full blur-xl floating-element opacity-30" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full blur-3xl floating-element opacity-20" style={{ animationDelay: '4s' }} />
        <div className="absolute bottom-40 right-1/3 w-20 h-20 bg-gradient-to-r from-yellow-100 to-orange-100 rounded-full blur-xl floating-element opacity-30" style={{ animationDelay: '6s' }} />
      </div>

      <div className="container-custom relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          {/* Student Community Section - Now First */}
          <div className="animate-on-scroll">
            <div className="pt-16 border-t border-gray-200">
              {/* Student Avatars Grid */}
              <div className="relative max-w-5xl mx-auto mb-16">
                <h3 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
                  Join Over <span className="text-blue-600">5 Million</span> Students
                </h3>

                {/* Global Student Community Map */}
                <div className="relative h-[450px] overflow-hidden rounded-2xl bg-white/80 backdrop-blur-sm border border-gray-200/50 shadow-xl">
                  {/* Clean World Map Background */}
                  <div className="absolute inset-0 flex items-center justify-center p-8">
                    <img
                      src="https://i.imgur.com/UX4ZDXc.png"
                      alt="World Map Background"
                      className="w-full h-full object-contain opacity-15 scale-100 filter grayscale"
                    />
                  </div>

                  {/* Minimal overlay for clarity */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-purple-50/20"></div>

                  {/* Clean Student Avatars with Professional Positioning */}

                  {/* North America - USA */}
                  <div className="absolute top-[25%] left-[20%] group">
                    <div className="w-14 h-14 rounded-full overflow-hidden border-3 border-white/90 shadow-lg bg-white/95 backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:shadow-xl">
                      <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" alt="Student from USA" className="w-full h-full object-cover" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-blue-500 rounded-full border-2 border-white shadow-md flex items-center justify-center">
                      <span className="text-xs">🇺🇸</span>
                    </div>
                  </div>

                  {/* Europe - Central */}
                  <div className="absolute top-[20%] left-[50%] transform -translate-x-1/2 group">
                    <div className="w-14 h-14 rounded-full overflow-hidden border-3 border-white/90 shadow-lg bg-white/95 backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:shadow-xl">
                      <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face" alt="Student from Europe" className="w-full h-full object-cover" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white shadow-md flex items-center justify-center">
                      <span className="text-xs">🇪🇺</span>
                    </div>
                  </div>

                  {/* Asia - East Asia */}
                  <div className="absolute top-[30%] right-[20%] group">
                    <div className="w-14 h-14 rounded-full overflow-hidden border-3 border-white/90 shadow-lg bg-white/95 backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:shadow-xl">
                      <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face" alt="Student from Asia" className="w-full h-full object-cover" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-red-500 rounded-full border-2 border-white shadow-md flex items-center justify-center">
                      <span className="text-xs">🇨🇳</span>
                    </div>
                  </div>

                  {/* Featured Student - Center */}
                  <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 group z-10">
                    <div className="w-16 h-16 rounded-full overflow-hidden border-3 border-white shadow-xl bg-white backdrop-blur-sm transition-all duration-300 hover:scale-110">
                      <img src="https://media.istockphoto.com/id/538650431/photo/high-school-or-college-graduate.jpg?s=612x612&w=0&k=20&c=3vd8-sdCVfbMXjU8-BgLcAqC0iZn3ykwyNwhYGFtCpA=" alt="Featured Student" className="w-full h-full object-cover" />
                    </div>
                    <div className="absolute inset-0 rounded-full border-2 border-blue-400/30 animate-pulse"></div>
                  </div>

                  {/* South America - Brazil */}
                  <div className="absolute bottom-[25%] left-[30%] group">
                    <div className="w-12 h-12 rounded-full overflow-hidden border-3 border-white/90 shadow-lg bg-white/95 backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:shadow-xl">
                      <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face" alt="Student from South America" className="w-full h-full object-cover" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-yellow-500 rounded-full border-2 border-white shadow-md flex items-center justify-center">
                      <span className="text-xs">🇧🇷</span>
                    </div>
                  </div>

                  {/* Africa - Central */}
                  <div className="absolute top-[55%] left-[52%] transform -translate-x-1/2 group">
                    <div className="w-12 h-12 rounded-full overflow-hidden border-3 border-white/90 shadow-lg bg-white/95 backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:shadow-xl">
                      <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face" alt="Student from Africa" className="w-full h-full object-cover" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-orange-500 rounded-full border-2 border-white shadow-md flex items-center justify-center">
                      <span className="text-xs">🌍</span>
                    </div>
                  </div>

                  {/* Australia */}
                  <div className="absolute bottom-[20%] right-[25%] group">
                    <div className="w-12 h-12 rounded-full overflow-hidden border-3 border-white/90 shadow-lg bg-white/95 backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:shadow-xl">
                      <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face" alt="Student from Australia" className="w-full h-full object-cover" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-purple-500 rounded-full border-2 border-white shadow-md flex items-center justify-center">
                      <span className="text-xs">🇦🇺</span>
                    </div>
                  </div>

                  {/* Asia - India */}
                  <div className="absolute top-[40%] right-[30%] group">
                    <div className="w-12 h-12 rounded-full overflow-hidden border-3 border-white/90 shadow-lg bg-white/95 backdrop-blur-sm transition-all duration-300 hover:scale-110 hover:shadow-xl">
                      <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face" alt="Student from India" className="w-full h-full object-cover" />
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-indigo-500 rounded-full border-2 border-white shadow-md flex items-center justify-center">
                      <span className="text-xs">🇮🇳</span>
                    </div>
                  </div>

                  {/* Subtle Connection Lines */}
                  <svg className="absolute inset-0 w-full h-full pointer-events-none opacity-20">
                    <defs>
                      <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.3"/>
                        <stop offset="100%" stopColor="#8b5cf6" stopOpacity="0.1"/>
                      </linearGradient>
                    </defs>

                    {/* Clean connection paths */}
                    <path d="M 160 120 Q 250 180 340 140" stroke="url(#connectionGradient)" strokeWidth="1.5" fill="none" strokeDasharray="5,5"/>
                    <path d="M 340 100 Q 420 160 500 120" stroke="url(#connectionGradient)" strokeWidth="1.5" fill="none" strokeDasharray="5,5"/>
                    <path d="M 200 300 Q 300 250 400 280" stroke="url(#connectionGradient)" strokeWidth="1.5" fill="none" strokeDasharray="5,5"/>
                  </svg>

                  {/* Minimal Learning Indicators */}
                  <div className="absolute top-[15%] left-[15%]">
                    <div className="w-3 h-3 bg-blue-400/60 rounded-full animate-pulse"></div>
                  </div>

                  <div className="absolute top-[25%] right-[15%]">
                    <div className="w-3 h-3 bg-green-400/60 rounded-full animate-pulse" style={{ animationDelay: '1s' }}></div>
                  </div>

                  <div className="absolute bottom-[25%] left-[25%]">
                    <div className="w-3 h-3 bg-purple-400/60 rounded-full animate-pulse" style={{ animationDelay: '2s' }}></div>
                  </div>

                  <div className="absolute bottom-[20%] right-[20%]">
                    <div className="w-3 h-3 bg-orange-400/60 rounded-full animate-pulse" style={{ animationDelay: '3s' }}></div>
                  </div>
                </div>

                <p className="text-center text-lg text-gray-600 max-w-3xl mx-auto mb-16">
                  Connect with learners from every continent. Our global community spans across
                  North America, Europe, Asia, Africa, South America, and Australia - all united
                  in their journey to master English.
                </p>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-5xl mx-auto">
                <div className="text-center group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-rose-500 to-pink-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">
                    1068
                  </div>
                  <div className="text-lg font-medium text-gray-700 mb-2">Online Courses</div>
                  <div className="text-sm text-gray-500">Available 24/7</div>
                </div>

                <div className="text-center group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-500 to-cyan-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">
                    225
                  </div>
                  <div className="text-lg font-medium text-gray-700 mb-2">Total Instructors</div>
                  <div className="text-sm text-gray-500">Expert Teachers</div>
                </div>

                <div className="text-center group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-green-500 to-emerald-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">
                    500K
                  </div>
                  <div className="text-lg font-medium text-gray-700 mb-2">Students Worldwide</div>
                  <div className="text-sm text-gray-500">Active Learners</div>
                </div>
              </div>
            </div>
          </div>

          {/* CTA Section - Now at Bottom */}
          <div className="animate-on-scroll pt-16 border-t border-gray-200 mt-16">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 rounded-full text-sm font-medium bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 mb-8 text-gray-700">
              <svg className="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              Start Your Journey Today
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold font-heading mb-8 leading-tight text-gray-900">
              Ready to Transform Your{' '}
              <span className="relative text-blue-600">
                English Skills?
                <svg className="absolute -bottom-2 left-0 w-full h-4" viewBox="0 0 300 12" fill="none">
                  <path d="M3 9C50 3 100 1 150 3C200 5 250 7 297 9" stroke="#3b82f6" strokeWidth="3" strokeLinecap="round" fill="none"/>
                </svg>
              </span>
            </h2>

            <p className="text-xl lg:text-2xl mb-12 text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Join over 10,000 students who have already improved their English proficiency
              with our proven learning methodology. Start your transformation today!
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center">
              <button className="group relative bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 font-bold text-lg px-10 py-5 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                <span className="flex items-center justify-center">
                  Start Free Trial
                  <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
              </button>

              <button className="group relative bg-white border-2 border-gray-300 text-gray-700 hover:border-blue-500 hover:text-blue-600 font-bold text-lg px-10 py-5 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                <span className="flex items-center justify-center">
                  <svg className="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  View Pricing Plans
                </span>
              </button>
            </div>
          </div>

        </div>
      </div>
    </section>
  );
};
