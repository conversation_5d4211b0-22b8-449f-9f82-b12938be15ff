import React, { useEffect, useRef } from 'react';
import { cn } from '../../utils';

interface CallToActionSectionProps {
  className?: string;
}

export const CallToActionSection: React.FC<CallToActionSectionProps> = ({ className }) => {
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate');
          }
        });
      },
      { threshold: 0.1 }
    );

    const animatedElements = sectionRef.current?.querySelectorAll('.animate-on-scroll, .animate-on-scroll-scale');
    animatedElements?.forEach((el) => observer.observe(el));

    return () => observer.disconnect();
  }, []);

  return (
    <section
      ref={sectionRef}
      className={cn('section-padding relative overflow-hidden bg-gradient-to-b from-gray-50 to-white', className)}
    >
      {/* Clean Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236366f1' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Subtle Floating Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full blur-2xl floating-element opacity-30" />
        <div className="absolute top-40 right-20 w-24 h-24 bg-gradient-to-r from-green-100 to-blue-100 rounded-full blur-xl floating-element opacity-30" style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-20 left-1/4 w-40 h-40 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full blur-3xl floating-element opacity-20" style={{ animationDelay: '4s' }} />
        <div className="absolute bottom-40 right-1/3 w-20 h-20 bg-gradient-to-r from-yellow-100 to-orange-100 rounded-full blur-xl floating-element opacity-30" style={{ animationDelay: '6s' }} />
      </div>

      <div className="container-custom relative z-10">
        <div className="max-w-6xl mx-auto text-center">
          {/* Main Content */}
          <div className="animate-on-scroll">
            {/* Badge */}
            <div className="inline-flex items-center px-6 py-3 rounded-full text-sm font-medium bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 mb-8 text-gray-700">
              <svg className="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
              🚀 Start Your Journey Today
            </div>

            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold font-heading mb-8 leading-tight text-gray-900">
              Ready to Transform Your{' '}
              <span className="relative text-blue-600">
                English Skills?
                <svg className="absolute -bottom-2 left-0 w-full h-4" viewBox="0 0 300 12" fill="none">
                  <path d="M3 9C50 3 100 1 150 3C200 5 250 7 297 9" stroke="#3b82f6" strokeWidth="3" strokeLinecap="round" fill="none"/>
                </svg>
              </span>
            </h2>

            <p className="text-xl lg:text-2xl mb-12 text-gray-600 max-w-4xl mx-auto leading-relaxed">
              Join over 10,000 students who have already improved their English proficiency
              with our proven learning methodology. Start your transformation today!
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="animate-on-scroll">
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16">
              <button className="group relative bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 font-bold text-lg px-10 py-5 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-2xl">
                <span className="flex items-center justify-center">
                  Start Free Trial
                  <svg className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
              </button>

              <button className="group relative bg-white border-2 border-gray-300 text-gray-700 hover:border-blue-500 hover:text-blue-600 font-bold text-lg px-10 py-5 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
                <span className="flex items-center justify-center">
                  <svg className="mr-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  View Pricing Plans
                </span>
              </button>
            </div>
          </div>

          {/* Student Community Section */}
          <div className="animate-on-scroll">
            <div className="pt-16 border-t border-gray-200">
              {/* Student Avatars Grid */}
              <div className="relative max-w-5xl mx-auto mb-16">
                <h3 className="text-3xl md:text-4xl font-bold text-center mb-12 text-gray-900">
                  Join Over <span className="text-blue-600">5 Million</span> Students
                </h3>

                {/* Floating Student Avatars */}
                <div className="relative h-80 overflow-hidden">
                  {/* Top Row */}
                  <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-20 h-20 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Second Row */}
                  <div className="absolute top-16 left-1/4 w-16 h-16 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element" style={{ animationDelay: '1s' }}>
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>
                  <div className="absolute top-16 right-1/4 w-16 h-16 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element" style={{ animationDelay: '2s' }}>
                    <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Third Row */}
                  <div className="absolute top-32 left-20 w-14 h-14 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element" style={{ animationDelay: '3s' }}>
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>
                  <div className="absolute top-32 right-20 w-14 h-14 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element" style={{ animationDelay: '4s' }}>
                    <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Fourth Row */}
                  <div className="absolute top-48 left-12 w-12 h-12 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element" style={{ animationDelay: '5s' }}>
                    <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>
                  <div className="absolute top-48 right-12 w-12 h-12 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element" style={{ animationDelay: '6s' }}>
                    <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Fifth Row */}
                  <div className="absolute top-64 left-1/3 w-14 h-14 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element" style={{ animationDelay: '7s' }}>
                    <img src="https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>
                  <div className="absolute top-64 right-1/3 w-14 h-14 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element" style={{ animationDelay: '8s' }}>
                    <img src="https://images.unsplash.com/photo-1557804506-669a67965ba0?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Additional scattered avatars */}
                  <div className="absolute top-24 left-8 w-10 h-10 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element" style={{ animationDelay: '9s' }}>
                    <img src="https://images.unsplash.com/photo-1531427186611-ecfd6d936c79?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>
                  <div className="absolute top-24 right-8 w-10 h-10 rounded-full overflow-hidden border-4 border-white shadow-lg floating-element" style={{ animationDelay: '10s' }}>
                    <img src="https://images.unsplash.com/photo-1524504388940-b1c1722653e1?w=150&h=150&fit=crop&crop=face" alt="Student" className="w-full h-full object-cover" />
                  </div>

                  {/* Decorative SVG Elements */}
                  <div className="absolute top-12 left-16 floating-element" style={{ animationDelay: '11s' }}>
                    <svg className="w-6 h-6 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="absolute top-12 right-16 floating-element" style={{ animationDelay: '12s' }}>
                    <svg className="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                  <div className="absolute bottom-12 left-24 floating-element" style={{ animationDelay: '13s' }}>
                    <svg className="w-7 h-7 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M3.172 5.172a4 4 0 015.656 0L10 6.343l1.172-1.171a4 4 0 115.656 5.656L10 17.657l-6.828-6.829a4 4 0 010-5.656z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="absolute bottom-12 right-24 floating-element" style={{ animationDelay: '14s' }}>
                    <svg className="w-6 h-6 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="absolute top-40 left-1/2 transform -translate-x-1/2 floating-element" style={{ animationDelay: '15s' }}>
                    <svg className="w-4 h-4 text-pink-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  </div>
                </div>

                <p className="text-center text-lg text-gray-600 max-w-3xl mx-auto mb-16">
                  Nullam at elementum odio auctor dui. Donec non nunc sodales massa
                  finibus tempor tortor magna feu nibh.
                </p>
              </div>

              {/* Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-5xl mx-auto">
                <div className="text-center group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-rose-500 to-pink-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">
                    1068
                  </div>
                  <div className="text-lg font-medium text-gray-700 mb-2">Online Courses</div>
                  <div className="text-sm text-gray-500">Available 24/7</div>
                </div>

                <div className="text-center group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-blue-500 to-cyan-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">
                    225
                  </div>
                  <div className="text-lg font-medium text-gray-700 mb-2">Total Instructors</div>
                  <div className="text-sm text-gray-500">Expert Teachers</div>
                </div>

                <div className="text-center group bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100">
                  <div className="text-5xl md:text-6xl font-bold mb-4 bg-gradient-to-r from-green-500 to-emerald-600 bg-clip-text text-transparent group-hover:scale-110 transition-transform">
                    500K
                  </div>
                  <div className="text-lg font-medium text-gray-700 mb-2">Students Worldwide</div>
                  <div className="text-sm text-gray-500">Active Learners</div>
                </div>
              </div>
            </div>
          </div>

          {/* Additional Trust Elements */}
          <div className="animate-on-scroll">
            <div className="flex flex-wrap items-center justify-center gap-8 mt-16 pt-8 border-t border-gray-200">
              <div className="flex items-center bg-white px-4 py-2 rounded-full shadow-sm border border-gray-100">
                <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium text-gray-700">SSL Secured</span>
              </div>
              <div className="flex items-center bg-white px-4 py-2 rounded-full shadow-sm border border-gray-100">
                <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium text-gray-700">Money-back Guarantee</span>
              </div>
              <div className="flex items-center bg-white px-4 py-2 rounded-full shadow-sm border border-gray-100">
                <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium text-gray-700">24/7 Support</span>
              </div>
              <div className="flex items-center bg-white px-4 py-2 rounded-full shadow-sm border border-gray-100">
                <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium text-gray-700">Instant Access</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
