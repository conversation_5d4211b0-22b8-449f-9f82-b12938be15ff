import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],

  // Optimize para sa static deployment
  build: {
    // Generate source maps para sa debugging
    sourcemap: true,

    // Optimize chunk splitting - para mas organized ang files
    rollupOptions: {
      output: {
        manualChunks: {
          // Separate vendor chunks para better caching
          vendor: ['react', 'react-dom'],
          utils: ['./src/utils/index.ts'],
        },
      },
    },

    // Optimize assets
    assetsInlineLimit: 4096, // Inline assets nga mas gamay sa 4kb
    cssCodeSplit: true, // Split CSS into separate files

    // Target modern browsers para mas gamay ang bundles
    target: 'es2015',

    // Minify para sa production
    minify: 'esbuild',
  },

  // Optimize dependencies
  optimizeDeps: {
    include: ['react', 'react-dom'],
  },

  // Configure base path para sa deployment
  base: './',

  // Preview server configuration
  preview: {
    port: 4173,
    host: true,
  },

  // Development server configuration
  server: {
    port: 5173,
    host: true,
    open: true,
  },
})
