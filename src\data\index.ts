import type { NavItem, FeatureItem, TestimonialItem } from '../types';

export const navigationItems: NavItem[] = [
  { label: 'Home', href: '/' },
  { label: 'About', href: '/about' },
  { label: 'Features', href: '#features' },
  { label: 'Testimonials', href: '#testimonials' },
  { label: 'Contact', href: '/contact' },
];

export const features: FeatureItem[] = [
  {
    id: '1',
    title: 'Speaking & Pronunciation',
    description: 'Master English pronunciation with AI-powered speech recognition and real-time feedback from native speakers.',
    icon: '🎯',
  },
  {
    id: '2',
    title: 'Grammar Mastery',
    description: 'Build strong grammar foundations with interactive lessons covering all English tenses and structures.',
    icon: '📚',
  },
  {
    id: '3',
    title: 'Vocabulary Building',
    description: 'Expand your vocabulary with contextual learning, spaced repetition, and real-world usage examples.',
    icon: '⚡',
  },
  {
    id: '4',
    title: 'Listening Comprehension',
    description: 'Improve your listening skills with authentic audio content from various English-speaking regions.',
    icon: '📊',
  },
  {
    id: '5',
    title: 'Writing Skills',
    description: 'Develop professional writing abilities through guided practice and automated feedback systems.',
    icon: '👥',
  },
  {
    id: '6',
    title: 'Cultural Context',
    description: 'Learn English within cultural contexts to communicate effectively in real-world situations.',
    icon: '📱',
  },
];

export const testimonials: TestimonialItem[] = [
  {
    id: '1',
    name: 'Maria Rodriguez',
    role: 'Student',
    company: 'University of Madrid',
    content: 'This ESL platform has transformed my English learning experience. The interactive lessons and personalized feedback have helped me improve significantly.',
    rating: 5,
  },
  {
    id: '2',
    name: 'Ahmed Hassan',
    role: 'Professional',
    company: 'Tech Solutions Inc.',
    content: 'As a working professional, I needed flexible learning options. This platform provided exactly what I needed to advance my career.',
    rating: 5,
  },
  {
    id: '3',
    name: 'Li Wei',
    role: 'Graduate Student',
    company: 'MIT',
    content: 'The pronunciation tools and real-time feedback have been invaluable for my academic presentations and daily communication.',
    rating: 4,
  },
];

export const companyStats = [
  { label: 'Students Enrolled', value: '10,000+' },
  { label: 'Lessons Completed', value: '500,000+' },
  { label: 'Success Rate', value: '95%' },
  { label: 'Countries Served', value: '50+' },
];
